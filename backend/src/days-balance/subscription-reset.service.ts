import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DayBalance } from '../users/schemas/token-balance.schema';
import { DaysBalanceService } from './days-balance.service';

@Injectable()
export class SubscriptionResetService {
  private readonly logger = new Logger(SubscriptionResetService.name);

  constructor(
    @InjectModel(DayBalance.name)
    private dayBalanceModel: Model<DayBalance>,
    private readonly daysBalanceService: DaysBalanceService,
  ) { }

  /**
   * Run daily at midnight to check for subscription resets
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async handleDailySubscriptionCheck(): Promise<void> {
    this.logger.log('Starting daily subscription reset check...');

    try {
      const now = new Date();

      // Find all users whose subscription needs to be reset
      const usersToReset = await this.dayBalanceModel.find({
        nextBillingDate: { $lte: now },
        subscriptionStatus: 'active',
      });

      this.logger.log(`Found ${usersToReset.length} users requiring subscription reset`);

      for (const userBalance of usersToReset) {
        try {
          await this.daysBalanceService.resetSubscriptionDays(userBalance.userId);
          this.logger.log(`Successfully reset subscription for user ${userBalance.userId}`);
        } catch (error) {
          this.logger.error(
            `Failed to reset subscription for user ${userBalance.userId}: ${error.message}`,
          );
        }
      }

      this.logger.log('Daily subscription reset check completed');
    } catch (error) {
      this.logger.error(`Error during daily subscription check: ${error.message}`);
    }
  }

  /**
   * Run weekly to clean up expired pack days
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async handleWeeklyPackCleanup(): Promise<void> {
    this.logger.log('Starting weekly pack cleanup...');

    try {
      // Find all users with pack days
      const usersWithPacks = await this.dayBalanceModel.find({
        packDays: { $gt: 0 },
      });

      this.logger.log(`Found ${usersWithPacks.length} users with pack days to check`);

      for (const userBalance of usersWithPacks) {
        try {
          await this.daysBalanceService.removeExpiredPackDays(userBalance.userId);
        } catch (error) {
          this.logger.error(
            `Failed to clean up expired packs for user ${userBalance.userId}: ${error.message}`,
          );
        }
      }

      this.logger.log('Weekly pack cleanup completed');
    } catch (error) {
      this.logger.error(`Error during weekly pack cleanup: ${error.message}`);
    }
  }

  /**
   * Manual trigger for subscription reset (for testing or admin use)
   */
  async manualSubscriptionReset(userId: string): Promise<void> {
    this.logger.log(`Manual subscription reset triggered for user ${userId}`);

    try {
      await this.daysBalanceService.resetSubscriptionDays(userId);
      this.logger.log(`Manual subscription reset completed for user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Manual subscription reset failed for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get subscription reset statistics
   */
  async getResetStatistics(): Promise<{
    usersNeedingReset: number;
    usersWithExpiredPacks: number;
    nextResetDate: Date | null;
  }> {
    const now = new Date();

    const [usersNeedingReset, usersWithExpiredPacks, nextReset] = await Promise.all([
      this.dayBalanceModel.countDocuments({
        nextBillingDate: { $lte: now },
        subscriptionStatus: 'active',
      }),
      this.dayBalanceModel.countDocuments({
        packDays: { $gt: 0 },
      }),
      this.dayBalanceModel.findOne(
        { nextBillingDate: { $gt: now } },
        {},
        { sort: { nextBillingDate: 1 } }
      ),
    ]);

    return {
      usersNeedingReset,
      usersWithExpiredPacks,
      nextResetDate: nextReset?.nextBillingDate || null,
    };
  }
}
