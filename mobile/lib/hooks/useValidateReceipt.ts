import { useEffect } from "react";
import { useIAP } from "react-native-iap";
import { useProductsConfig } from "./use-products-config";
import { CommonService } from "../services/common.service";
import { API_URL } from "../constants";
import { getIAPErrorMessage } from "../utils/iap-errors";
import { Alert, Platform } from "react-native";

const commonService = new CommonService();
export const useValidateReceipt = () => {
    const productConfigs = useProductsConfig();
    const {
        currentPurchase,
        currentPurchaseError,
        finishTransaction,
    } = useIAP();

    // Handle purchase updates
    useEffect(() => {
        console.log("Current purchase: ", currentPurchase);
        if (currentPurchase && productConfigs.length > 0) {
            const handlePurchase = async () => {
                try {
                    const platform = Platform.OS as 'ios' | 'android';
                    const config = productConfigs.find(config =>
                        config.productId[platform] === currentPurchase.productId
                    )

                    if (!config) {
                        console.error('Unknown product purchased:', currentPurchase.productId);
                        Alert.alert(
                            'Purchase Error',
                            'Unknown product purchased. Please contact support if this issue persists.'
                        );
                        return;
                    }

                    // Validate purchase with backend
                    const headers = await commonService.setTokenInHeaders();
                    const response = await fetch(`${API_URL}/payments/validate`, {
                        method: 'POST',
                        headers,
                        body: JSON.stringify({
                            receipt: currentPurchase.transactionReceipt,
                            productId: currentPurchase.productId,
                            packageId: config.id,
                            platform: Platform.OS,
                            packageType: config.type === 'consumable' ? 'day_refill' : 'subscription',
                            originalTransactionId: currentPurchase.originalTransactionIdentifierIOS,
                            transactionId: currentPurchase.transactionId,
                            transactionDate: currentPurchase.transactionDate,
                            ...(config.type === 'subscription'
                                ? { daysPerMonth: config.daysPerMonth }
                                : { days: config.days }
                            ),
                        }),
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(errorData.message || 'Purchase validation failed');
                    }

                    await response.json(); // Consume the response


                    // Finish the transaction
                    await finishTransaction({
                        purchase: currentPurchase,
                        isConsumable: config?.type === 'consumable',
                    });

                } catch (error) {
                    console.error('Purchase processing failed:', error);

                    // Show user-friendly error message
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Purchase processing failed. Please try again.';

                    Alert.alert(
                        'Purchase Error',
                        errorMessage,
                        [
                            { text: 'OK' },
                            {
                                text: 'Contact Support',
                                onPress: () => {
                                    // TODO: Add support contact functionality
                                    console.log('Contact support requested');
                                }
                            }
                        ]
                    );
                }
            };

            handlePurchase();
        }
    }, [currentPurchase, productConfigs]);

    useEffect(() => {
        if (currentPurchaseError) {
            const message = getIAPErrorMessage(currentPurchaseError.message);
            if (message) {
                Alert.alert('Purchase Error', message);
            }
        }
    }, [currentPurchaseError]);
}